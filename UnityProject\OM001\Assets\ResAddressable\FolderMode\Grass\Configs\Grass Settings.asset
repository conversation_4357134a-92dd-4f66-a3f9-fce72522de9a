%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c489dfd837945034c98c8ccaa0daba75, type: 3}
  m_Name: Grass Settings
  m_EditorClassIdentifier: 
  shaderToUse: {fileID: 7200000, guid: ba1b94a9474830b4ba296433166f046a, type: 3}
  materialToUse: {fileID: 2100000, guid: 1a5a7f1f66a39554683cfd7bf3e10935, type: 2}
  grassRandomHeightMin: 1
  grassRandomHeightMax: 2
  bladeRadius: 0.3
  bladeForwardAmount: 0.38
  bladeCurveAmount: 2
  bottomWidth: 1
  MinWidth: 0.05
  MinHeight: 0.1
  MaxWidth: 0.1
  MaxHeight: 0.6
  windSpeed: 2
  windStrength: 0.02
  allowedBladesPerVertex: 4
  allowedSegmentsPerBlade: 4
  affectStrength: 1
  topTint: {r: 0.39382136, g: 1, b: 0, a: 1}
  bottomTint: {r: 0.14905155, g: 0.509434, b: 0.07929869, a: 1}
  overrideColor: {r: 0, g: 1, b: 0, a: 1}
  drawBounds: 0
  minFadeDistance: 40
  maxDrawDistance: 125
  cullingTreeDepth: 4
  cuttingParticles: {fileID: 4576011973974011265, guid: 75c70fe8e965b28448cfa822c5b0b72b,
    type: 3}
  castShadow: 0
